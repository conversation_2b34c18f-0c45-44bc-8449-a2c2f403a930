using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Odrc.Dots.Attributes;
using Odrc.Dots.Controllers;
using Odrc.Dots.Utilities;
using Odrc.Dots.Business.Common;
using Odrc.Dots.Business.Rh;
using Odrc.Dots.Core.Log;
using Odrc.Dots.Entities.Common;
using Odrc.Dots.Areas.Transfers.Models.Mosci;
using Odrc.Dots.Business.Transfers;
using Odrc.Dots.Entities.Transfers;
using System.Reflection;
using System.Security.Cryptography;
using Newtonsoft.Json;
using System.EnterpriseServices;

namespace Odrc.Dots.Areas.Transfers.Controllers
{
    public class MosciController : BaseController
    {
        
       //GET: Transfers/Mosci/Mosci
       [HttpGet]
        [DotsAuthorize("IMosc", true)]
        public ActionResult Mosci(string searchPrefix = "", string searchOffenderId = "", string action = "", string from = "")
        {
            var model = new MosciPageViewModel();

            //Ensure dropdown  options are always populate
            LoadToDropdown(model);

            if (!string.IsNullOrEmpty(ScreenData.Oid))
            {
                // If the search ID starts with a letter (prefix), extract it
                if (ScreenData.Oid.Length > 0 && char.IsLetter(ScreenData.Oid[0]))
                {
                    searchPrefix = ScreenData.Oid[0].ToString().ToUpper();
                    searchOffenderId = ScreenData.Oid.Substring(1).Trim();
                    action = "Search";
                    
                }
                
            }

            // Handle search action
            if (action == "Search" && !string.IsNullOrWhiteSpace(searchOffenderId))
            {
                model.SearchPrefix = searchPrefix ?? "A";
                model.SearchOffenderId = searchOffenderId;

                // Perform search - combine prefix and offender ID for search
                var combinedSearchId = model.SearchPrefix + searchOffenderId;
                var searchResults = PerformInmateSearchFromCombined(combinedSearchId);
                var offenderData = OffenderLogic.GetOffender(combinedSearchId);

                // Clear the inmates list and add search results
                model.Inmates.Clear();
                

                if (searchResults.Any())
                {
                    // Add all search results
                    foreach (var result in searchResults)
                    {
                        model.Inmates.Add(new MosciViewModel
                        {
                           
                            SchDate = result.SchDate,
                            SchdInst = result.SchdInst,
                            Instno = result.Instno,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                            Oid = result.Oid,
                            Descrl = result.Descrl,
                            Mant = result.Mant,
                            Stationame = result.Stationame,
                            SysDate = result.SysDate,
                            Rowid = result.Rowid,
                            LastName = offenderData.LastName,
                            FirstName = offenderData.FirstName,
                            InmateIdPrefix = result.InmateIdPrefix,
                            OffenderId = model.SearchOffenderId,
                            CombinedOffenderId = combinedSearchId,
                        });
                    }

                    
                } else if (offenderData != null)
                {
                    var institutionId = 0;
                    var institutionOptions = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                        .Where(x => x.Value != "0");

                    var matchingInstitution = institutionOptions.FirstOrDefault(x => x.Text.Trim().Equals(offenderData.InstAbbr?.Trim(), StringComparison.OrdinalIgnoreCase));
                    if (matchingInstitution != null && int.TryParse(matchingInstitution.Value.ToString(), out institutionId))
                    {
                        // Institution ID found

                    }

                    var inmateToAdd = new MosciViewModel
                    {
                        
                        //Instno = offenderData.InstAbbr,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                        Instname = offenderData.InstAbbr,
                        Oid = offenderData.Oid,
                        Instno = institutionId,
                       
                        LastName = offenderData.LastName,
                        FirstName = offenderData.FirstName,
                        InmateIdPrefix = model.SearchPrefix,
                        OffenderId = model.SearchOffenderId,
                        CombinedOffenderId = combinedSearchId,
                    };

                    model.Inmates.Add(inmateToAdd);
                }
                else
                {
                    // No results found - add one empty row
                    //model.Inmates.Add(CreateEmptyInmate());
                    model.HasSearchResults = false;
                    model.ErrorMessage = "No inmates found matching the search criteria.";
                }
            }
            else if (action == "AddNew")
            {
                // Add a new empty row
                if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                {
                    model.Inmates.Add(CreateEmptyInmate());
                }
                else
                {
                    model.ErrorMessage = $"Maximum number of rows ({MosciPageViewModel.MaxRows}) reached. Cannot add more rows.";
                }
            }
            else
            {
                // Default view - show one empty row
                //model.Inmates.Add(CreateEmptyInmate());
            }
            LoadDefaultData(model);
            if (from == "Inmate Save")
            {
                model.Message = "Inmate scheduled successfully.";
            }
            return View(model);
        }

        private void LoadToDropdown(MosciPageViewModel model)
        {
            if (model.SchdInstDrp == null || !model.SchdInstDrp.Any())
            {
                model.SchdInstDrp = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                                            .Select(x => new SelectListItem { Value = x.Value.ToString(), Text = x.Text })
                                            .ToList();
            }
            if (model.InstnoDrp == null || !model.InstnoDrp.Any())
            {
                model.InstnoDrp = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts().Where(x => x.Value != "0")
                                            .Select(x => new SelectListItem { Value = x.Value.ToString(), Text = x.Text })
                                            .ToList();
            }
        }

        // Helper method to create an empty inmate
        private MosciViewModel CreateEmptyInmate()
        {
            return new MosciViewModel
            {
                SchDate = DateTime.MinValue,
                SysDate = DateTime.Now,
                InmateIdPrefix = "", // Don't set default prefix to avoid showing "A" in empty rows
                SchdInst = 0,
                Instno = 0,
                Instname = "",
                Oid = "",
                Descrl = "",
                Mant = "",
                Stationame = "",
                Rowid = "",
                LastName = "",
                FirstName = "",
                OffenderId = "",

            };

        }

        // Helper method to perform inmate search from combined ID (prefix + offender ID)
        private List<MosciViewModel> PerformInmateSearchFromCombined(string combinedId)
        {
            var matchingInmates = new List<MosciViewModel>();

            if (!string.IsNullOrWhiteSpace(combinedId))
            {
                // Extract the prefix (first character) and the numeric part
                string prefix = "";
                string offenderId = combinedId.Trim();

                // If the search ID starts with a letter (prefix), extract it
                if (combinedId.Length > 0 && char.IsLetter(combinedId[0]))
                {
                    prefix = combinedId[0].ToString().ToUpper();
                    offenderId = combinedId.Substring(1).Trim();
                }


                var data = MosciLogic.GetMosciInfoByOaksId(combinedId);
                foreach (var mosci in data)
                {
                    matchingInmates.Add(new MosciViewModel
                    {
                        Oid = mosci.Oid,
                        SchdInst = mosci.SchdInst,
                        Instno = mosci.Instno,
                        SchDate = mosci.SchDate,
                        Descrl = mosci.Descrl,
                    });
                }
            }

            return matchingInmates;
        }

        // POST: Handle form submissions for various actions
        [HttpPost]
        [DotsAuthorize("IMosc", true)]
        public ActionResult Mosci(MosciPageViewModel model, string submitAction = "")
        {
            try
            {
                if (model.SchdInstDrp == null || !model.SchdInstDrp.Any() || model.InstnoDrp == null || !model.InstnoDrp.Any())
                {
                    LoadToDropdown(model);
                }

                LoadDefaultData(model);

                // For search operations, we need to clear the model state to prevent
                // the existing table data from interfering with search results
                if (submitAction == "Search")
                {
                    // Clear all model state related to Inmates to prevent binding issues
                    var keysToRemove = ModelState.Keys.Where(k => k.StartsWith("Inmates")).ToList();
                    foreach (var key in keysToRemove)
                    {
                        ModelState.Remove(key);
                    }

                    // Ensure we have a fresh model for search
                    model.Inmates = new List<MosciViewModel>();
                }

                switch (submitAction)
                {
                    case "Search":
                        return HandleSearch(model);
                    
                    case "Save":
                        return HandleSave(model);
                    case "Cancel":
                        return RedirectToAction(actionName: "Index", 
                            controllerName: "InmateInformationCard", routeValues: new{ area = "Transfers" });
                    default:
                        return View(model);
                }
            }
            catch (Exception ex)
            {
                model.ErrorMessage = "An error occurred: " + ex.Message;
                return View(model);
            }
        }

        
        
         // POST: Transfers/Mosci/GetOffenderData - For JavaScript auto-population
        [HttpPost]
        public JsonResult GetOffenderData(string prefix, string offenderId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(offenderId))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Offender ID cannot be empty."
                    });
                }
                var combinedOffenderId = prefix + offenderId;
                //check if the inmate is already scheduled
                var scheduleInmate = MosciLogic.GetMosciInfoByOaksId(combinedOffenderId);
                var schinmateexisting = scheduleInmate.FirstOrDefault();
                if (schinmateexisting != null && schinmateexisting.SchDate != null)
                {
                    return Json(new
                    {
                        success = true,
                        message = "Inmate already schedule."
                    });
                }
                // Find matching offender data
                var matchingInmates = OffenderLogic.GetOffender(combinedOffenderId);

                if (matchingInmates != null)
                {
                    
                    // Find the institution ID from the dropdown options
                    var institutionId = 0;
                    var institutionOptions = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                        .Where(x => x.Value != "0");

                    var matchingInstitution = institutionOptions.FirstOrDefault(x => x.Text.Trim().Equals(matchingInmates.InstAbbr?.Trim(), StringComparison.OrdinalIgnoreCase));
                    if (matchingInstitution != null && int.TryParse(matchingInstitution.Value.ToString(), out institutionId))
                    {
                        // Institution ID found
                    }

                    return Json(new
                    {
                        success = true,
                        offender = new
                        {
                            lastName = matchingInmates.LastName,
                            firstName = matchingInmates.FirstName,
                            frominsText = matchingInmates.InstAbbr,
                            instno = institutionId

                        }
                    });

                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = "No matching offender found."
                    });
                }
            }
            catch (Exception ex)
            { 
                return Json(new
                {
                    success = false,
                    message = "Error retrieving offender data: " + ex.Message
                });
            }
        }
        
        [HttpPost]
        
        private ActionResult HandleSearch(MosciPageViewModel model)
        {
            if (model.SchdInstDrp == null || !model.SchdInstDrp.Any() || model.InstnoDrp == null || !model.InstnoDrp.Any())
            {
                LoadToDropdown(model);
            }

            if (string.IsNullOrWhiteSpace(model.SearchOffenderId))
            {
                model.ErrorMessage = "Please enter an Offender ID.";
                // Create a fresh list with one empty row
                //model.Inmates = new List<MosciViewModel> { CreateEmptyInmate() };
                model.Inmates = new List<MosciViewModel> ();
                model.HasSearchResults = false;
                return View(model);
            }

            // Combine prefix and offender ID for search
            var combinedSearchId = model.SearchPrefix + model.SearchOffenderId;
            SaveActiveOffenderId(combinedSearchId);
            var searchResults = PerformInmateSearchFromCombined(combinedSearchId);
            var offenderData = OffenderLogic.GetOffender(combinedSearchId);

            // Create a completely new list to ensure clean state
            model.Inmates = new List<MosciViewModel>();

            if (searchResults.Any() && offenderData!=null)
            {
                // Add all search results to the inmates list
                foreach (var result in searchResults)
                {
                    // Create a new instance to avoid reference issues
                    var inmateToAdd = new MosciViewModel
                    {
                        SchDate = result.SchDate,
                        SchdInst = result.SchdInst,
                        Instno = result.Instno,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                        Oid = result.Oid,
                        Descrl = result.Descrl,
                        Mant = result.Mant,
                        Stationame = result.Stationame,
                        SysDate = result.SysDate,
                        Rowid = result.Rowid,
                        LastName = offenderData.LastName,
                        FirstName = offenderData.FirstName,
                        InmateIdPrefix = result.InmateIdPrefix,
                        OffenderId = model.SearchOffenderId,
                        CombinedOffenderId = combinedSearchId,
                    };

                    model.Inmates.Add(inmateToAdd);
                }
                
                model.HasSearchResults = true;
                
                // Check if search results contain scheduled inmates (inmates with schedule dates set)
                //var hasScheduledInmates = searchResults.Any(r => r.SchDate != DateTime.MinValue);
                //model.HasScheduledInmates = hasScheduledInmates;

                //model.Message = $"Found {searchResults.Count} inmate(s) matching the search criteria.";
            }
            else if (offenderData != null)
            {
                var institutionId = 0;
                var institutionOptions = Areas.User.Utilities.HelperMethods.GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
                    .Where(x => x.Value != "0");

                var matchingInstitution = institutionOptions.FirstOrDefault(x => x.Text.Trim().Equals(offenderData.InstAbbr?.Trim(), StringComparison.OrdinalIgnoreCase));
                if (matchingInstitution != null && int.TryParse(matchingInstitution.Value.ToString(), out institutionId))
                {
                    // Institution ID found
                   
                }
                //if mot search found bind default values of inmaete
                // Create a new instance to avoid reference issues
                var inmateToAdd = new MosciViewModel
                {
                    //Instno = offenderData.InstAbbr,  // offenderData.InstAbbr -- current instituion of offender eg: 'CCI'
                    Instname= offenderData.InstAbbr,
                    Oid = offenderData.Oid,
                    Instno = institutionId,
                    
                    LastName = offenderData.LastName,
                    FirstName = offenderData.FirstName,
                    InmateIdPrefix = model.SearchPrefix,
                    OffenderId = model.SearchOffenderId,
                    CombinedOffenderId = combinedSearchId,
                };

                    model.Inmates.Add(inmateToAdd);
                

            }
            else
            {
                // No search results found - show one empty row
                //model.Inmates.Add(CreateEmptyInmate());
                model.HasSearchResults = false;
                model.ErrorMessage = "No inmates found matching the search criteria.";
            }
        
            // Clear ViewData to ensure clean rendering for search results
            //ViewData.Clear();

            return View(model);
        }

        [HttpPost]
        private ActionResult HandleSave(MosciPageViewModel model)
        {
            try
            {
                // Verify User access
                var user = UserLogic.GetUserById(HttpContext.User.Identity.Name);
                if (user == null)
                {
                    model.ErrorMessage = "User authentication failed. Please log in again.";
                    return View(model);
                }

                // Validate that we have inmates to save
                if (model.Inmates == null || !model.Inmates.Any())
                {
                    model.ErrorMessage = "No inmate data found to save. Please add at least one inmate.";
                    return View(model);
                }

                // Filter out inmates with incomplete data and validate required fields
                var validInmates = new List<MosciViewModel>();
                var errorMessages = new List<string>();
                int rowNumber = 1;

                foreach (var inmate in model.Inmates)
                {
                    if (inmate != null)
                    {
                        
                        var combinedId = inmate.InmateIdPrefix + inmate.OffenderId;
                        // Check if the offender id exist 
                        var offenderData = OffenderLogic.GetOffender(combinedId);
                        // if not exist empty the offender id. Avoid incorrect offender id save
                        if(offenderData == null)
                        {
                            inmate.OffenderId = "";
                        }

                        // Check if this inmate has the minimum required data (OffenderId)
                        if (!string.IsNullOrWhiteSpace(inmate.OffenderId))
                        {
                            // Validate required fields for save operation
                            var validationErrors = new List<string>();

                            if (string.IsNullOrWhiteSpace(inmate.InmateIdPrefix))
                                validationErrors.Add($"Row {rowNumber}: Prefix is required");

                            if (string.IsNullOrWhiteSpace(inmate.LastName))
                                validationErrors.Add($"Row {rowNumber}: Last Name is required");

                            if (string.IsNullOrWhiteSpace(inmate.FirstName))
                                validationErrors.Add($"Row {rowNumber}: First Name is required");

                            if (!inmate.SchdInst.HasValue || inmate.SchdInst.Value <= 0)
                                validationErrors.Add($"Row {rowNumber}: Destination Institution is required");

                            if (!inmate.Instno.HasValue || inmate.Instno.Value <= 0)
                                validationErrors.Add($"Row {rowNumber}: From Institution is required");

                            if (inmate.SchDate == DateTime.MinValue)
                                validationErrors.Add($"Row {rowNumber}: Schedule Date is required");

                            // Check if From and To institutions are the same
                            if (inmate.Instno.HasValue && inmate.SchdInst.HasValue &&
                                inmate.Instno.Value == inmate.SchdInst.Value)
                            {
                                validationErrors.Add($"Row {rowNumber}: From and To institutions cannot be the same");
                            }

                            if (validationErrors.Any())
                            {
                                errorMessages.AddRange(validationErrors);
                            }
                            else
                            {
                                validInmates.Add(inmate);
                            }
                        }
                        
                    }
                    rowNumber++;
                }

                // If there are validation errors, return them
                if (errorMessages.Any())
                {
                    model.ErrorMessage = "Please correct the following errors:\n" + string.Join("\n", errorMessages);
                    return View(model);
                }

                // If no valid inmates found after validation
                if (!validInmates.Any())
                {
                    model.ErrorMessage = "No valid inmate data found to save. Please ensure at least one inmate has all required fields filled.";
                    return View(model);
                }

                // Process each valid inmate
                var savedCount = 0;
                var processingErrors = new List<string>();

                foreach (var inmate in validInmates)
                {
                    try
                    {
                        var combineOffenderId = inmate.InmateIdPrefix + inmate.OffenderId;

                        // Check if this inmate already exists in MOSCI
                        var offenderData = MosciLogic.GetMosciInfoByOaksId(combineOffenderId);
                        var existing = offenderData.FirstOrDefault();

                        // Format the schedule date properly
                        var scheduleDateString = inmate.SchDate.ToString("yyyy-MM-dd");

                        if (existing != null && !string.IsNullOrWhiteSpace(existing.Rowid))
                        {
                            // Update existing record
                            var updateResult = MosciLogic.InsertorUpdateMosci(
                                combineOffenderId,
                                scheduleDateString,
                                inmate.Instno.Value,
                                inmate.SchdInst.Value,
                                inmate.Descrl ?? "",
                                user.UserName,
                                existing.Rowid
                            );

                            if (updateResult > 0)
                            {
                                savedCount++;
                            }
                            else
                            {
                                processingErrors.Add($"Failed to update inmate {combineOffenderId}");
                            }
                        }
                        else
                        {
                            // Insert new record
                            var insertResult = MosciLogic.InsertorUpdateMosci(
                                combineOffenderId,
                                scheduleDateString,
                                inmate.Instno.Value,
                                inmate.SchdInst.Value,
                                inmate.Descrl ?? "",
                                user.UserName,
                                null
                            );

                            if (insertResult > 0)
                            {
                                savedCount++;
                            }
                            else
                            {
                                processingErrors.Add($"Failed to save inmate {combineOffenderId}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        processingErrors.Add($"Error processing inmate {inmate.InmateIdPrefix}{inmate.OffenderId}: {ex.Message}");
                    }
                }

                // Prepare response message
                if (savedCount > 0)
                {
                    if (processingErrors.Any())
                    {
                        model.Message = $"{savedCount} inmate(s) scheduled successfully.";
                        model.ErrorMessage = "Some errors occurred:\n" + string.Join("\n", processingErrors);
                    }
                    else
                    {
                        //model.Message = $"{savedCount} inmate(s) scheduled successfully.";
                    }
                }
                else
                {
                    model.ErrorMessage = "Failed to save inmates.\n" + string.Join("\n", processingErrors);
                }

                //Maintain only top row after Save
                if (model.Inmates.Count > 0)
                {
                    var firstInmate = model.Inmates[0];
                    model.Inmates.Clear();
                    model.Inmates.Add(firstInmate);
                }


                // Ensure dropdown data is available for the fresh view
                if (model.SchdInstDrp == null || !model.SchdInstDrp.Any() || model.InstnoDrp == null || !model.InstnoDrp.Any())
                {
                    LoadToDropdown(model);
                }
                LoadDefaultData(model);

                if (!string.IsNullOrEmpty(ScreenData.Oid))
                {
                    return RedirectToAction("Mosci", new {from = "Inmate Save"});
                }
                else
                {
                    return View(model);
                }

                //return View(model);

            }
            catch (Exception ex)
            {
                model.ErrorMessage = "An unexpected error occurred while saving: " + ex.Message;
                return View(model);
            }
        }



        [HttpPost]
        public JsonResult DeleteMosciRecord(MosciPageViewModel model)
        {
            try
            {
                // Extract the first inmate marked for deletion from the model
                var inmateToDelete = model?.Inmates?.FirstOrDefault(i => i.IsMarkedForDeletion);

                if (inmateToDelete == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "No inmate marked for deletion found in the request."
                    });
                }

                // Validate required parameters
                if (string.IsNullOrWhiteSpace(inmateToDelete.OffenderId))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Offender ID cannot be empty."
                    });
                }

                if (string.IsNullOrWhiteSpace(inmateToDelete.InmateIdPrefix))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Prefix cannot be empty."
                    });
                }

                if (!inmateToDelete.IsMarkedForDeletion)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Record must be marked for deletion."
                    });
                }

                // Combine prefix and offender ID
                var combineOffenderId = inmateToDelete.InmateIdPrefix + inmateToDelete.OffenderId;

                if (inmateToDelete.SchDate !=null )
                {
                    var data = MosciLogic.CheckIfOffenderExists(combineOffenderId, inmateToDelete.SchDate.ToString("yyyy-MM-dd"));
                    if(data == true)
                    {
                        return Json(new
                        {
                            success = false,
                            message = "You must remove the inmate from TRAN3 before you will be permitted to proceed to delete him from MOSCI."
                        });
                    }
                }

                // Get current user
                var user = UserLogic.GetUserById(HttpContext.User.Identity.Name);
                if (user == null)
                {
                    return Json(new
                    {
                        success = false,
                        message = "User not found."
                    });
                }

                

                // Get existing record to find the RowId
                var offenderData = MosciLogic.GetMosciInfoByOaksId(combineOffenderId);
                var existing = offenderData.FirstOrDefault();

                if (existing == null || string.IsNullOrWhiteSpace(existing.Rowid))
                {
                    return Json(new
                    {
                        success = false,
                        message = "Record not found or no valid Row ID."
                    });
                }

                // Delete the record
                var result = MosciLogic.DeleteMosciRecord(existing.Rowid, user.UserName);

                if (result > 0)
                {
                    return Json(new
                    {
                        success = true,
                        message = "Inmate deleted successfully from MOSCI."
                     });

                }
                else
                {
                    return Json(new
                    {
                        success = false,
                        message = "Failed to delete the record."
                    });
                }
            }
            catch (Exception ex)
            {
                
                return Json(new
                {
                    success = false,
                    message = "An error occurred while deleting the record: " + ex.Message
                });
            }
        }

        private void LoadDefaultData(MosciPageViewModel vm)
        {
            
            vm.PrefixOptions = Helper.GetPrefix(ScreenData.DefaultInst.ToString());
            
        }
        // POST: Check if offender ID is eligible for deletion (has rowid value)
        [HttpPost]
        public JsonResult CheckDeleteEligibility(string prefix, string offenderId)
         {
            try
            {
                if (string.IsNullOrWhiteSpace(offenderId))
                {
                    return Json(new
                    {
                        success = false,
                        eligible = false,
                        message = "Offender ID cannot be empty."
                    });
                }

                if (string.IsNullOrWhiteSpace(prefix))
                {
                    return Json(new
                    {
                        success = false,
                        eligible = false,
                        message = "Prefix cannot be empty."
                    });
                }

                var combinedOffenderId = prefix + offenderId;

                // Get MOSCI record for this offender ID
                var mosciRecords = MosciLogic.GetMosciInfoByOaksId(combinedOffenderId);
                var mosciRecord = mosciRecords.FirstOrDefault();

                if (mosciRecord == null)
                {
                    return Json(new
                    {
                        success = true,
                        eligible = false,
                        message = "No MOSCI record found for this offender ID."
                    });
                }

                // Check if Mant field equals 'CHG' (case-insensitive)
                //bool isEligible = !string.IsNullOrWhiteSpace(mosciRecord.Mant) &&
                //                 mosciRecord.Mant.Trim().Equals("CHG", StringComparison.OrdinalIgnoreCase);

                //Check if rowid field and its schedule 
                bool isEligible = !string.IsNullOrWhiteSpace(mosciRecord.Rowid);
                //if (mosciRecord.SchDate != null)
                //{
                //    return Json(new
                //    {
                //        success = false,
                //        eligible = false,
                //        message = "Delete button disabled - its a scheduled inmate."
                //    });
                //}
                //else
                //{
                    return Json(new
                    {
                        success = true,
                        eligible = isEligible,
                        message = isEligible ? "Delete button enabled - Rowid field has value." : "Delete button disabled - Rowid field has no value."
                    });
                //}
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    success = false,
                    eligible = false,
                    message = "An error occurred while checking delete eligibility: " + ex.Message
                });
            }
        }

    }
}